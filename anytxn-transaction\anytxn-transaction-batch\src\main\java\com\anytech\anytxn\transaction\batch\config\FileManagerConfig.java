package com.anytech.anytxn.transaction.batch.config;

import com.anytech.anytxn.file.config.AnytxnFilePathConfig;
import com.anytech.anytxn.file.enums.FilePathConfigTypeEnum;
import com.anytech.anytxn.file.utils.ValueProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: sukang
 * @Date: 2021/11/2 16:28
 */
@Configuration
public class FileManagerConfig {


    @Bean
    public AnytxnFilePathConfig rbsInFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.rbsFileFormat",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig rbsInFileSecondPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.rbsFileSecondFormat",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig glFileFormatPathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.glFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }


    @Bean
    public AnytxnFilePathConfig additionalFieldsFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.collAddDemoAccOut",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig creditBureauIdUpdateFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.creditIdUpdateFileFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig atFileFormatPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.atFileFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig daaaFileFormatPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.daaaFileFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig cdaFileFormatPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.cdaFileFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig cdFileFormatPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.cdFileFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig dbsInFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.dbsIn",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig reportOutFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.reportOut",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig collectionFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.collection",
                FilePathConfigTypeEnum.INPUT, filePathValue);
    }


    @Bean
    public AnytxnFilePathConfig giroResultFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.girofile",
                FilePathConfigTypeEnum.INPUT, filePathValue);
    }


    @Bean
    public AnytxnFilePathConfig accountFileFormatXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.accountFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig accountFileFormatXmlCheckFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.accountFileCheck",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }


    @Bean
    public AnytxnFilePathConfig outstandingBalanceUploadFileFormatXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.accountFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }


    @Bean
    public AnytxnFilePathConfig creditLimitFileFormatXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.accountFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig mbsToDcsFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.mbsToDcs",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig dcsToMbsFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.dcsToMbs",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig originalMobilFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.originalMobil",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig iataFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.iata",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig axsFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.axsFileFormat",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig reportFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.reportFileFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig siaFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.siaFileFormat",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig repostGlFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.repostGl",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig bonusPointInPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.bonusPointInFormat",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig bonusPointWritePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.bonusPointWriteFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig statementFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.statementFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig transAmountFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.transAmountFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig btiIncomingFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiFilePath",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig btiOutgoingFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig btiIdChangeFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig btiOutgoingMergeFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiMergeFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }
    /*@Bean
    public AnytxnFilePathConfig btiIncomingReportPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiReportFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }*/


    @Bean
    public AnytxnFilePathConfig accountXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiAcctXmlFile",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig cusAcctInfoFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.cusAcctInfoFilePath",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig creditLimitUploadFileFormatXmlFileConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.creditLimitUploadFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig caaFileFormatPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.caaFileFormatPath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig cashBackFileInPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.cashBackFileInPath",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig cashBackRespOutFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.cashBackRespOutFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig btiFlagFileFormatXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiFlag",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig creditBureauDQAccountFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiDQAccountFileFormat",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig iDUpdateConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.btiIdUpdateFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig acmsXmlFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.acmsXmlFileFormat",
                FilePathConfigTypeEnum.INPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig anytechXmlFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.anytechXmlFileFormat",
                FilePathConfigTypeEnum.INPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig mergeXmlFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.mergeXmlFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig btiAnytechXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.anytechXmlFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig btiAcmsXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.acmsXmlFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig btiMergeXmlFileConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.mergeXmlFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean
    public AnytxnFilePathConfig journalFilePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.journalFileFormat",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig gvFileFormatPathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.corpGvFileFormat",
                FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }


    @Bean
    public AnytxnFilePathConfig migrationSettlePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.migrationSettleFile",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig interestPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.interestErrorPath",
                FilePathConfigTypeEnum.INPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig interestToUpdatePathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.interestToUpdatePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }

    @Bean
    public AnytxnFilePathConfig loyaltyCashBackPathConfig(ValueProvider nacosValueProvider) {
        return new AnytxnFilePathConfig("anytxn-file-path.loyalty.cashbackFilePath",
                FilePathConfigTypeEnum.OUTPUT, nacosValueProvider);
    }


    @Bean
    public AnytxnFilePathConfig loyaltyTransInputFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.loyalty.transFilePath", FilePathConfigTypeEnum.INPUT, filePathValue);
    }

    @Bean(name = "loyaltyStatementCrpFilePathConfig")
    public AnytxnFilePathConfig loyaltyStatementCrpFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.loyalty.statementCrpFilePath", FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

    @Bean(name = "partnerMarginFilePathConfig")
    public AnytxnFilePathConfig partnerMarginFilePathConfig(ValueProvider filePathValue) {
        return new AnytxnFilePathConfig("anytxn-file-path.transaction.partnerMarginFile", FilePathConfigTypeEnum.OUTPUT, filePathValue);
    }

}
